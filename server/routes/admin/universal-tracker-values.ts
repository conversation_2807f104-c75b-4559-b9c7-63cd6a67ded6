/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import UniversalTrackerValue, { UniversalTrackerValuePlain } from '../../models/universalTrackerValue';
import { ActionList } from '../../service/utr/constants';
import UniversalTrackerActionManager from "../../service/utr/UniversalTrackerActionManager"
import { AuthRouter } from '../../http/AuthRouter';
import { UniversalTrackerValueRepository } from '../../repository/UniversalTrackerValueRepository';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import { AnyBulkWriteOperation } from 'mongoose';
import { wwgLogger } from '../../service/wwgLogger';
import ContextError from '../../error/ContextError';

const router = express.Router() as AuthRouter;

router.route('/')
  .get(function (req, res) {
    UniversalTrackerValue.find()
      .sort({ effectiveDate: 'asc', type: 'asc', initiativeId: 'asc', universalTrackerId: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:id')
  .put((req, res) => {
    if (!req.body._id || req.params.id !== req.body._id) {
      return res.Invalid('POST url id (' + req.params.id + ') does not match POST body _id (' + req.body._id + ')');
    }
    UniversalTrackerValueRepository.mustFindById(req.params.id)
      .then((obj) => {
        obj.set(req.body);
        return obj.save();
      })
      .then(() => res.Success('Successfully updated UniversalTrackerValue with _id=' + req.params.id))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/initiativeId/:initiativeId')
  .get(function (req, res) {
    UniversalTrackerValue.find({ initiativeId: req.params.initiativeId }, '-history -disaggregations')
      .sort({ effectiveDate: 'asc', type: 'asc', initiativeId: 'asc', universalTrackerId: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/universalTrackerId/:universalTrackerId')
  .get(function (req, res) {
    UniversalTrackerValue.find({ universalTrackerId: req.params.universalTrackerId }, '-history -disaggregations')
      .sort({ effectiveDate: 'asc', type: 'asc', initiativeId: 'asc', universalTrackerId: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/universalTrackerId/:universalTrackerId/initiativeId/:initiativeId')
  .get(function (req, res) {
    UniversalTrackerValue.find({ universalTrackerId: req.params.universalTrackerId, initiativeId: req.params.initiativeId },
      '-history -disaggregations')
      .sort({ effectiveDate: 'asc', type: 'asc', initiativeId: 'asc', universalTrackerId: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/importAndReplace')
  .post(ContextMiddleware, function (req, res) {
    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on importAndReplace');
    }

    UniversalTrackerValue.find(
      {
        universalTrackerId: req.body.universalTrackerId, initiativeId: req.body.initiativeId,
        type: req.body.type, status: { $in: [ActionList.Created, ActionList.Updated, ActionList.Verified] }, effectiveDate: req.body.effectiveDate
      },
      '-history -disaggregations')
      .sort({ created: 'asc' })
      .exec()
      .then(async (models) => {

        const requestValue = isNaN(req.body.value) || req.body.value==='' ? undefined : Number(req.body.value);
        req.body.value = undefined;

        delete req.body.status; // Not allowed, and will fall back to model default
        const user = req.user;
        if (models.length === 0) {
          const model = new UniversalTrackerValue(req.body);
          return model.save()
            .then(async (obj) => {
              try {
                await UniversalTrackerActionManager.setValueById(obj._id, user, requestValue);
                if (obj.verificationRequired) {
                  await UniversalTrackerActionManager.verifyId(obj._id, user); // Auto verify only with importer
                }
                return res.FromModel('Created');
              } catch (e) {
                console.log(e);
                return res.Invalid('Created document successfully, but could not update value. Please do it manually');
              }
            })
            .catch((err: Error) => {
              return res.Exception(err);
            });
        }

        let updated = false;
        const latestModel = models.pop();
        if (latestModel && requestValue !== latestModel.value) {
          updated = true;
          try {
            await UniversalTrackerActionManager.setValueById(latestModel._id, user, requestValue);
            if (latestModel.verificationRequired) {
              await UniversalTrackerActionManager.verifyId(latestModel._id, user);
            }
          } catch (e) {
            console.log(e);
            return res.Invalid('Could not update value');
          }
        } else if (latestModel && latestModel.status !== ActionList.Verified) {
          updated = true;
          try {
            await UniversalTrackerActionManager.verifyId(latestModel._id, user);
          } catch (e) {
            console.log(e);
            return res.Invalid('Could not update value');
          }
        }

        for (const model of models) {
          try {
            await UniversalTrackerActionManager.rejectId(model._id, user);
          } catch (e) {
            console.log(e);
            return res.Invalid('Could not update value');
          }
        }
        return res.FromModel(updated ? 'Updated' : 'Skipped');
      })
      .catch((e: Error) => res.Exception(e));
  });

router.route('/source/:sourceType/:surveyCode')
  .get(function (req, res) {
    UniversalTrackerValue.find({ sourceType: req.params.sourceType, sourceCode: req.params.surveyCode },
      { created: 1, status: 1, type: 1, value: 1, initiativeId: 1, universalTrackerId: 1, disaggregationSummary: 1 })
      .sort({ effectiveDate: 'asc', type: 'asc', initiativeId: 'asc', universalTrackerId: 'asc' })
      .lean()
      .exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/rejectAll/universalTrackerId/:universalTrackerId/sourceCode/:sourceCode')
  .patch(async function (req, res) {
    const utrValues = await UniversalTrackerValue.find({
        universalTrackerId: req.params.universalTrackerId,
        sourceCode: req.params.sourceCode,
        status: { $ne: ActionList.Rejected },
      }).lean().exec();

    const promises: Promise<any>[] = [];
    const user = req.user;
    utrValues.forEach((utrValue) => promises.push(
      UniversalTrackerActionManager.rejectId(utrValue._id, user).catch(console.log)
    )
    );

    const result = await Promise.all(promises);
    return res.Success('Rejected ' + result.length + ' values');
  });

router.route('/verifyAll/universalTrackerId/:universalTrackerId/sourceCode/:sourceCode')
  .patch(async function (req, res) {
    const utrValues = await UniversalTrackerValue.find(
      { universalTrackerId: req.params.universalTrackerId, sourceCode: req.params.sourceCode, status: ActionList.Updated })
      .lean().exec();

    const promises: Promise<any>[] = [];
    const user = req.user;
    utrValues.forEach((utrValue) => promises.push(
      UniversalTrackerActionManager.verifyId(utrValue._id, user).catch(console.log)
    ));

    const result = await Promise.all(promises);
    return res.Success('Verified ' + result.length + ' values');
  });

const MAX_LIMIT = 10000;
const DEFAULT_LIMIT = 1000;
function deepCompareExceptEvidenceData(before: any, after: any, path = ''): string[] {
  const differences: string[] = [];

  // Get all keys from both objects, excluding evidenceData, evidenceDataMigrated, and legacy lastUpdateDate
  const excludedKeys = ['evidenceData', 'evidenceDataMigrated', 'lastUpdateDate'];
  const beforeKeys = Object.keys(before || {}).filter(k => !excludedKeys.includes(k));
  const afterKeys = Object.keys(after || {}).filter(k => !excludedKeys.includes(k));

  // Check for added keys (excluding evidenceData/evidenceDataMigrated)
  const addedKeys = afterKeys.filter(k => !beforeKeys.includes(k));
  if (addedKeys.length > 0) {
    differences.push(`${path}: Added unexpected keys: ${addedKeys.join(', ')}`);
  }

  // Check for removed keys
  const removedKeys = beforeKeys.filter(k => !afterKeys.includes(k));
  if (removedKeys.length > 0) {
    differences.push(`${path}: Removed keys: ${removedKeys.join(', ')}`);
  }

  // Check for modified values in existing keys
  for (const key of beforeKeys) {
    if (afterKeys.includes(key)) {
      const beforeValue = before[key];
      const afterValue = after[key];
      const currentPath = path ? `${path}.${key}` : key;

      if (Array.isArray(beforeValue) && Array.isArray(afterValue)) {
        if (beforeValue.length !== afterValue.length) {
          differences.push(`${currentPath}: Array length changed from ${beforeValue.length} to ${afterValue.length}`);
        } else {
          for (let i = 0; i < beforeValue.length; i++) {
            const itemDiffs = deepCompareExceptEvidenceData(beforeValue[i], afterValue[i], `${currentPath}[${i}]`);
            differences.push(...itemDiffs);
          }
        }
      } else if (typeof beforeValue === 'object' && beforeValue !== null && typeof afterValue === 'object' && afterValue !== null) {
        const objectDiffs = deepCompareExceptEvidenceData(beforeValue, afterValue, currentPath);
        differences.push(...objectDiffs);
      } else if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {
        differences.push(`${currentPath}: Value changed from ${JSON.stringify(beforeValue)} to ${JSON.stringify(afterValue)}`);
      }
    }
  }

  return differences;
}

router.route('/migrate/evidence').post(async (req, res) => {
  // Temporarily put the handler here instead of moving to a service
  // because this will be soon moved after the migration
  // TODO: Remove after the migration
  const parsedQueryLimit = Number(req.query.limit);
  let limit: number;

  if (isNaN(parsedQueryLimit) || parsedQueryLimit > MAX_LIMIT) {
    limit = DEFAULT_LIMIT;
  } else {
    limit = parsedQueryLimit;
  }

  wwgLogger.info(`Migrating evidence for utrvs. Limit: ${limit}`);

  // Step 1: Load data before migration and keep in memory
  const utrvs = await UniversalTrackerValue.find({
    evidenceDataMigrated: { $exists: false },
    $or: [{ evidenceData: { $exists: false } }, { 'history.evidenceData': { $exists: false } }],
  })
    .limit(limit)
    .orFail()
    .lean()
    .exec();

  // Store original data for comparison (deep clone)
  const originalData = JSON.parse(JSON.stringify(utrvs));
  const documentIds = utrvs.map(doc => doc._id);

  wwgLogger.info(`Loaded ${utrvs.length} documents for migration. Keeping original data in memory for verification.`);

  // Step 2: Prepare and execute migration
  const updates: AnyBulkWriteOperation<UniversalTrackerValuePlain>[] = utrvs.map((utrv) => {
    const evidenceData =
      utrv.evidence && !utrv.evidenceData ? utrv.evidence.map((id) => ({ documentId: id })) : utrv.evidenceData;
    const utrvHistory = utrv.history.map((history) => {
      if (!history.evidence || history.evidenceData) {
        return history;
      }
      return {
        ...history,
        evidenceData: history.evidence.map((id) => ({ documentId: id })),
      };
    });

    return {
      updateOne: {
        filter: {
          _id: utrv._id,
          __v: utrv.__v, // Ensure we do not override data
        },
        update: {
          $set: {
            evidenceData,
            history: utrvHistory,
            evidenceDataMigrated: new Date(),
          },
        },
        upsert: false,
      },
    };
  });

  const result = await UniversalTrackerValue.bulkWrite(updates, { 
    strict: false,
    writeConcern: { 
      w: 'majority', // Wait for majority of replica set members to acknowledge
      j: true,       // Wait for write to be committed to journal
      wtimeout: 30000 // Timeout after 30 seconds
    }
  });

  wwgLogger.info(`Migration completed. Found: ${utrvs.length}, Modified: ${result.modifiedCount}. Starting verification...`);

  // Step 3: Reload data from database and compare
  const updatedDocs = await UniversalTrackerValue.find({
    _id: { $in: documentIds }
  })
    .lean()
    .read('primary') // Ensure we read from primary to get latest data
    .exec();

  // Create a Map for faster lookup of updated documents
  const updatedDocsMap = new Map(
    updatedDocs.map(doc => [doc._id.toString(), doc])
  );

  // Step 4: Verify that only evidenceData was added
  const verificationResults: Array<{
    utrvId: string;
    isValid: boolean;
    differences: string[];
  }> = [];

  for (const originalDoc of originalData) {
    const originalId = originalDoc._id.toString() as string;
    const updatedDoc = updatedDocsMap.get(originalId);

    if (!updatedDoc) {
      verificationResults.push({
        utrvId: originalId,
        isValid: false,
        differences: ['Document not found after migration']
      });
      continue;
    }

    const differences = deepCompareExceptEvidenceData(originalDoc, updatedDoc);

    verificationResults.push({
      utrvId: originalId,
      isValid: differences.length === 0,
      differences
    });
  }

  const invalidDocs = verificationResults.filter(r => !r.isValid);
  const validDocs = verificationResults.filter(r => r.isValid);

  wwgLogger.info(`Verification completed. Valid: ${validDocs.length}, Invalid: ${invalidDocs.length}`);

  if (invalidDocs.length > 0) {
    wwgLogger.error(new ContextError('Migration verification failed for some documents:', {
      invalidCount: invalidDocs.length,
      invalidDocs: invalidDocs.slice(0, 5) // Log first 5 for debugging
    }));
  }

  return res.FromModel({
    utrvs: utrvs.map(({ _id }) => String(_id)),
    modifiedCount: result.modifiedCount,
    upsertedCount: result.upsertedCount,
    matchedCount: result.matchedCount,
    verification: {
      totalChecked: verificationResults.length,
      validDocuments: validDocs.length,
      invalidDocuments: invalidDocs.length,
      success: invalidDocs.length === 0,
      issues: invalidDocs.slice(0, 10) // Return first 10 invalid docs in response
    }
  });
});

module.exports = router;
