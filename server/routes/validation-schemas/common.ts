import { ObjectId } from 'bson';
import { isValidObjectId } from 'mongoose';
import { z } from 'zod';

export const idSchema = z.string().refine(
  (value) => {
    return ObjectId.isValid(value);
  },
  { message: 'Invalid id' }
);

export const stringIdSchema = (name: string = 'id') =>
  z.string().refine(
    (strId) => isValidObjectId(strId),
    () => ({ message: `${name} is not valid ObjectId` })
  );

export const refineIdSchema = (name: string = 'id') =>
  z
    .string()
    .refine(
      (strId) => isValidObjectId(strId),
      () => ({ message: `${name} is not valid ObjectId` })
    )
    .transform((strId) => new ObjectId(strId));

export const getObjectIdsSchema = <IsOptional extends boolean = false>({
  min = 0,
  isOptional = false as IsOptional,
}: { min?: number; isOptional?: IsOptional } = {}) => {
  const schema = z
    .string()
    .array()
    .min(min)
    .refine(
      (ids) => ids.every((strId) => isValidObjectId(strId)),
      (strId) => ({ message: `Contains invalid id: [${strId.toString()}]` })
    )
    .transform((ids) => ids.map((id) => new ObjectId(id)));

  return (isOptional ? schema.optional() : schema) as IsOptional extends true
    ? z.ZodOptional<typeof schema>
    : typeof schema;
};

export const objectIdsSchema = getObjectIdsSchema({ min: 1, isOptional: true });
