/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import type { UniversalTrackerPublic } from "../../models/public/universalTrackerType";
import type { UserMin, UserModel } from "../../models/user";
import type { InitiativePlain } from "../../models/initiative";
import type { AddressPlain } from "../../models/organization";
import type { ObjectId } from "bson";
import type { UniversalTrackerValuePlain } from "../../models/universalTrackerValue";
import type { UniversalTrackerValuePublic } from "../../models/public/universalTrackerValueType";
import type { DataPeriods } from "../utr/constants";
import type { IntegrationConnectionPlain } from "../../models/integrationConnection";


export type GeneratedUtr = Pick<UniversalTrackerPublic,
  | 'code'
  | 'valueLabel'
  | 'name'
  | 'valueType'
  | 'valueValidation'
  | 'instructions'
  | 'numberScale'
  | 'unit'
  | 'unitType'
  | 'type'
  | '_id'
>;

export type GeneratedUtrv = Pick<UniversalTrackerValuePublic,
  | 'value'
  | 'valueType'
  | 'valueData'
  | 'sourceType'
  | 'effectiveDate'
  | 'period'
  | 'type'
  | 'status'
  | 'assuranceStatus'
> & { utrCode: string, universalTrackerId: string };

export type IntegrationData = {
  utrsData: { integrationCode: string, utr: GeneratedUtr; utrvs: GeneratedUtrv[]; }[]
}

export type IntegrationConfig = {
  type: 'external';
  requirements: {
    /** These are UTR Like questions that are for now created on the fly **/
    questions: GeneratedUtr[];
  }
};

/**
 * It has similarities with vendor type, but it's specific vendor integration provider
 */
export interface IntegrationProvider {
  /**
   * Partner code used as slug in urls, should be limited to a-z0-9 and - only
   */
  code: string;
  name: string;
  shortName: string;
  logo: string;
  logoFooter?: string;
  tags: string[];
  description: string;
  highlights: string[];
  link: string;
  color?: string;
  icon?: string;
  integration?: IntegrationConfig;
}


export interface Answers extends Pick<UniversalTrackerValuePlain, 'value' | 'valueData'>{
  utrCode: string,
}

type IntegrationInitiative = Pick<InitiativePlain, '_id' | 'name' | 'geoLocation' | 'country' | 'profile' | 'industry' | 'parentId'>;

export interface SetupData {
  integrationConnection: IntegrationConnectionPlain;
  /** Custom name for the connection */
  name?: string;
  providerCode: string;
  user: Pick<UserModel, '_id' | 'jobTitle' | 'firstName' | 'surname' | 'email'>;
  rootInitiative: IntegrationInitiative;
  // Initiative tree of children companies **includes root initiative**
  initiativeTree?: IntegrationInitiative[];
  // Simplified UTR answers
  generatedAnswers: Answers[];
  address?: AddressPlain;
}


type ProviderIntegrationStatus =
  // Setup is not completed
  | 'setup_required'
  // Setup is pending (3rd party is processing)
  | 'pending'
  // Setup is completed and ready to use
  | 'active'
  // Error occurred during setup
  | 'error';

interface ProviderSetupConfig {
  provider: IntegrationProvider,
  integration?: IntegrationConfig
}

export interface ExternalAppInfo {
  name: string;
  login?: {
    url: string;
    text?: string;
  }
}

export interface ProviderSetupInfo extends ProviderSetupConfig {
  status: ProviderIntegrationStatus;
  externalApp?: ExternalAppInfo;
}

export interface IntegrationConnectionInfo<D = unknown> extends ProviderSetupConfig {
  connection: IntegrationConnectionPlain<D> & { creator: UserMin };
}

export interface IntegrationCheck {
  initiativeId: ObjectId;
  integrationCode: string;
}

export interface IntegrationDataParams<T extends IntegrationConnectionPlain = IntegrationConnectionPlain> {
  period: DataPeriods | undefined;
  startDate: string | undefined;
  endDate: string | undefined;
  utrCodes: string[];
  isCompleted?: boolean;
  connection: T;
}

export interface IntegrationService {

  code: string;

  /**
   * Optional setupCheck for provider that need to execute periodically
   * Ideally webhook or some kind of other notification should be supported.
   */
  executeChecks?: () => Promise<unknown>;

  getInfo(): IntegrationProvider;

  getSetupConfig(): Promise<ProviderSetupConfig>;

  createSetup(setupData: SetupData): Promise<{ success: boolean, data?: unknown }>;

  getAvailableQuestions(): Promise<GeneratedUtr[]>;

  generateIntegrationData(dataLookup: IntegrationDataParams): Promise<IntegrationData["utrsData"]>

  /**
   * If app support external application
   */
  getExternalApp?: () => Promise<ExternalAppInfo>;
}



