/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { ProductCodes } from '../../../models/customer';
import { AppCode, AppConfig, defaultCTEnterpriseFeatures } from '../AppConfig';
import { PERMISSION_GROUPS } from '../../../models/initiative';
import { defaultCTBase } from './defaultCTBase';
import { DownloadUtrvStatusCombined } from '../../../types/download';
import { DisplayOption } from '../../../types/download';
import { ActionList } from '../../../service/utr/constants';
import { FeatureTag } from '@g17eco/core';

export const defaultCTEnterprise: AppConfig = {
  ...defaultCTBase,
  code: AppCode.CompanyTrackerEnterprise,
  productCode: ProductCodes.CompanyTrackerEnterprise,
  validProductCodes: [ProductCodes.CompanyTrackerEnterprise],
  permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_ENTERPRISE,
  name: 'Company Tracker Enterprise',
  onboardingPath: 'company-tracker-enterprise',
  settings: {
    ...defaultCTBase.settings,
    // No additional recommendations
    overviewRecommendedAddons: [],
    settingsRecommendedAddons: ['ctl'],
    baseFeatures: defaultCTEnterpriseFeatures,
    betaFeatures: [
      FeatureTag.MetricAssistantAI,
      FeatureTag.DraftFurtherExplanationAI,
      FeatureTag.AIAccessDocumentLibrary,
      FeatureTag.AIAutoAnswer,
      FeatureTag.SDGInsightAI,
      FeatureTag.PPTXReportAI,
    ],
    canViewAllPacks: true,
    canEditInsightOverview: true,
    canShowSDGDetails: true,
    defaultDownloadOptions: {
      metricStatuses: [DownloadUtrvStatusCombined.All, DownloadUtrvStatusCombined.AllAnswered, ActionList.Verified],
      metricOverrides: [DisplayOption.UserInput, DisplayOption.MetricOverrides, DisplayOption.SystemDefault],
    },
  },
};
