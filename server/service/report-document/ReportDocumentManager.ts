/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { LoggerInterface, wwgLogger } from '../wwgLogger';
import ReportDocument, { CreateReportDocument } from '../../models/reportDocument';
import { ObjectId } from 'bson';
import { CSRDLexicalStateGenerator, getCSRDLexicalStateGenerator } from '../reporting/csrd/CSRDLexicalStateGenerator';
import { UserPlain } from '../../models/user';
import { SerializedEditorState } from 'lexical';

export class ReportDocumentManager {
  constructor(private readonly logger: LoggerInterface, private csrdLexicalStateGenerator: CSRDLexicalStateGenerator) {}

  public async create(createData: CreateReportDocument) {
    const document = new ReportDocument(createData);
    return document.save();
  }

  public async deleteReport({ reportId, initiativeId }: { reportId: string; initiativeId: string }) {
    this.logger.warn('Deleting report document', { reportId, initiativeId });

    return ReportDocument.findOneAndDelete({
      initiativeId: new ObjectId(initiativeId),
      _id: new ObjectId(reportId),
    })
      .orFail()
      .exec();
  }

  public async getTemplate({ reportId, user }: { reportId: string; user: UserPlain }) {
    const reportDocument = await ReportDocument.findById(new ObjectId(reportId)).orFail().lean();

    if (reportDocument.type === 'csrd') {
      return this.csrdLexicalStateGenerator.getTemplate({
        initiativeId: reportDocument.initiativeId,
        user,
      });
    }

    return;
  }

  public async download({ reportId, editorState }: { reportId: string; editorState: SerializedEditorState }) {
    const reportDocument = await ReportDocument.findById(new ObjectId(reportId)).orFail().lean();

    if (reportDocument.type === 'csrd') {
      return this.csrdLexicalStateGenerator.downloadReport({
        reportDocument,
        editorState,
      });
    }

    return undefined;
  }
}

let instance: ReportDocumentManager;
export const getReportDocumentManager = () => {
  if (!instance) {
    instance = new ReportDocumentManager(wwgLogger, getCSRDLexicalStateGenerator());
  }
  return instance;
};
