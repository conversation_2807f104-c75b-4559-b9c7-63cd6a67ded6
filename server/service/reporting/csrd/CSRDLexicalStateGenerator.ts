/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { LoggerInterface, wwgLogger } from '../../wwgLogger';
import Survey, { SurveyModelPlain } from '../../../models/survey';
import { UserPlain } from '../../../models/user';
import { InitiativePlain } from '../../../models/initiative';
import UniversalTrackerValue from '../../../models/universalTrackerValue';
import { universalTrackerLookup } from '../../../repository/utrvAggregations';
import { excludeSoftDeleted } from '../../../repository/aggregations';
import { createHeadlessEditor } from '@lexical/headless';
import { HeadingNode } from '@lexical/rich-text';
import { ListItemNode, ListNode } from '@lexical/list';
import { $getRoot, SerializedEditorState, type CreateEditorArgs } from 'lexical';
import { IXBRLNode } from '../lexical/nodes/IXBRLNode';
import { XbrlTracker } from '../XbrlTracker';
import { GeneratorParameters, ReportSection, SectionData } from '../xhtml/csrdTypes';
import UtrExternalMapping from '../../../models/utrExternalMapping';
import { ReportDocumentPlain } from '../../../models/reportDocument';
import UserError from '../../../error/UserError';

// Import section builders
import { buildIntro } from './intro';
import { buildE1Section } from './sections/E1';
import { buildE2Section } from './sections/E2';
import { buildE3Section } from './sections/E3';
import { buildE4Section } from './sections/E4';
import { buildE5Section } from './sections/E5';
import { buildS1Section } from './sections/S1';
import { buildS2Section } from './sections/S2';
import { buildS3Section } from './sections/S3';
import { buildS4Section } from './sections/S4';
import { buildG1Section } from './sections/G1';
import { buildGeneralInformation } from './sections/general';
import { buildEnvironmentalInformation } from './sections/environmental/environmental';
import { EsrsFact, UtrvData, XBRLMapping } from './types';
import { getDefaultMapping, lexicalNodeToSectionChild } from './utils';
import { getCSRDGenerator } from './CSRDGenerator';

const editorConfig: CreateEditorArgs = {
  onError: (error: Error) => {
    wwgLogger.error(error);
  },
  theme: {
    ltr: 'ltr',
    rtl: 'rtl',
    placeholder: 'editor-placeholder',
    paragraph: 'editor-paragraph',
    list: {
      nested: {
        listitem: 'editor-nested-listitem',
      },
      ol: 'editor-list-ol',
      ul: 'editor-list-ul',
      listitem: 'editor-listitem',
    },
    text: {
      bold: 'editor-text-bold',
      italic: 'editor-text-italic',
      overflowed: 'editor-text-overflowed',
      underline: 'editor-text-underline',
    },
    ixbrlNode: 'ixbrl-plugin-node',
  },
  namespace: 'ReportEditor',
  nodes: [ListNode, ListItemNode, HeadingNode, IXBRLNode],
};

interface GenerateLexicalStateParams
  extends Pick<GeneratorParameters, 'initiative' | 'survey' | 'mapping' | 'utrCodeToUtrvMap'> {
  user: Pick<UserPlain, '_id'>;
  preview?: boolean;
}

export class CSRDLexicalStateGenerator {
  constructor(private logger: LoggerInterface, private csrdGenerator: ReturnType<typeof getCSRDGenerator>) {}

  public async generateCSRDLexicalState(params: GenerateLexicalStateParams) {
    const { initiative, survey, mapping, utrCodeToUtrvMap } = params;

    this.logger.info(`Generating Lexical state for survey: ${survey._id}`, {
      surveyId: survey._id,
      initiativeId: survey.initiativeId,
    });

    const tracker = new XbrlTracker();
    const sectionData: SectionData = {
      initiative,
      mapping,
      utrCodeToUtrvMap,
      tracker,
    };

    // Use Lexical headless editor to build the state
    const editor = createHeadlessEditor(editorConfig);
    editor.update(
      () => {
        $getRoot().append(
          ...buildGeneralInformation(sectionData),
          ...buildIntro(sectionData),
          ...buildEnvironmentalInformation(sectionData),
          ...buildE1Section(sectionData),
          ...buildE2Section(sectionData),
          ...buildE3Section(sectionData),
          ...buildE4Section(sectionData),
          ...buildE5Section(sectionData),
          ...buildS1Section(sectionData),
          ...buildS2Section(sectionData),
          ...buildS3Section(sectionData),
          ...buildS4Section(sectionData),
          ...buildG1Section(sectionData)
        );
      },
      { discrete: true }
    );

    return editor.getEditorState().toJSON();
  }

  private async getUtrvData(survey: SurveyModelPlain) {
    const aggregations = [
      {
        $match: {
          _id: { $in: survey.visibleUtrvs },
          ...excludeSoftDeleted(),
        },
      },
      universalTrackerLookup,
      {
        $project: {
          _id: 1,
          value: 1,
          valueData: 1,
          status: 1,
          effectiveDate: 1,
          universalTracker: {
            $arrayElemAt: ['$universalTracker', 0],
          },
        },
      },
    ];
    return UniversalTrackerValue.aggregate<UtrvData>(aggregations).exec();
  }

  private async getGeneratorParameters(initiativeId: ObjectId) {
    // Get latest available survey
    const survey = await Survey.findOne({ initiativeId, deletedDate: { $exists: false } })
      .populate('initiative')
      .sort({ effectiveDate: -1, created: -1 })
      .lean<SurveyModelPlain>()
      .exec();

    if (!survey) {
      throw new UserError('No survey found for the initiative associated with this report document.');
    }

    if (!survey.initiative) {
      throw new UserError('Unable to fetch details of selected survey. If this continues please contact our support.');
    }

    const externalMapping = await UtrExternalMapping.find({ type: 'csrd' }).lean();
    const externalXbrlMapping = externalMapping.reduce((acc, item) => {
      if (!acc[item.mappingCode]) {
        acc[item.mappingCode] = {
          factName: item.mappingCode as EsrsFact,
          utrCode: item.utrs[0].utrCode,
          valueListCode: item.utrs[0].valueListCode,
        };
      }
      return acc;
    }, {} as XBRLMapping);

    // Merge default mapping with external mapping
    const mapping = getDefaultMapping(externalXbrlMapping);

    const utrvData = await this.getUtrvData(survey);
    const utrCodeToUtrvMap = new Map<string, UtrvData>(utrvData.map((utrv) => [utrv.universalTracker.code, utrv]));

    return { initiative: survey.initiative, survey, mapping, utrvData, utrCodeToUtrvMap };
  }

  public async getTemplate({
    initiativeId,
    user,
  }: {
    initiativeId: ObjectId;
    user: UserPlain;
  }): Promise<SerializedEditorState> {
    const params = await this.getGeneratorParameters(initiativeId);
    const state = await this.generateCSRDLexicalState({ ...params, user });
    return state;
  }

  private buildReportSections(editorState: SerializedEditorState) {
    // Use Lexical headless editor to build the state
    const editor = createHeadlessEditor(editorConfig);
    const parsedEditorState = editor.parseEditorState(editorState);
    editor.setEditorState(parsedEditorState);

    const sections: ReportSection[] = [];
    editor.update(() => {
      const root = $getRoot();
      const children = root.getChildren();

      let currentSection: ReportSection | null = null;

      for (const node of children) {
        if (node.getType() === 'heading') {
          // Start a new section
          if (currentSection) {
            sections.push(currentSection);
          }
          currentSection = {
            header: { title: node.getTextContent() },
            children: [],
          };
        } else {
          // Add to current section's children
          if (currentSection) {
            currentSection.children.push(lexicalNodeToSectionChild(node));
          }
        }
      }
      // Push the last section
      if (currentSection) {
        sections.push(currentSection);
      }
    });

    return sections;
  }

  public async downloadReport({
    reportDocument,
    editorState,
  }: {
    reportDocument: ReportDocumentPlain;
    editorState: SerializedEditorState;
  }) {
    const params = await this.getGeneratorParameters(reportDocument.initiativeId);
    const sections = this.buildReportSections(editorState);
    return this.csrdGenerator.generateCSRD({ ...params, sections });
  }
}

let instance: CSRDLexicalStateGenerator;
export const getCSRDLexicalStateGenerator = () => {
  if (!instance) {
    instance = new CSRDLexicalStateGenerator(wwgLogger, getCSRDGenerator());
  }
  return instance;
};
