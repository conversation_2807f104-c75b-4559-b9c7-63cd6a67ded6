import { getContextualIxbrlNodes, getStringData, MAJOR_SECTION } from '../utils';
import { LexicalNode } from 'lexical/LexicalNode';
import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode } from 'lexical';
import { SectionData } from '../../xhtml/csrdTypes';

export function buildE4Section(sectionData: SectionData): LexicalNode[] {
  const { mapping, utrCodeToUtrvMap } = sectionData;
  const heading = $createHeadingNode('h2');
  heading.append($createTextNode('[E4-3] Actions and resources related to biodiversity and ecosystems'));

  const para = $createParagraphNode();
  para.append(
    $createTextNode(
      getStringData({
        factName:
          'esrs:DisclosureOfBiodiversityAndEcosystemsrelatedActionsAndResourcesAllocatedToTheirImplementationExplanatory',
        mapping,
        utrCodeToUtrvMap,
      })
    )
  );

  const contextualIxbrlNodes = getContextualIxbrlNodes({
    ...sectionData,
    majorSection: MAJOR_SECTION.E4.code,
  });

  return [heading, para, ...contextualIxbrlNodes];
}
