import { $createTextNode } from 'lexical';
import { $createHeadingNode } from '@lexical/rich-text';
import { LexicalNode } from 'lexical/LexicalNode';
import { SectionData } from '../../xhtml/csrdTypes';
import { getContextualIxbrlNodes, MAJOR_SECTION } from '../utils';

export function buildE2Section(sectionData: SectionData): LexicalNode[] {
  const heading = $createHeadingNode('h1');
  heading.append($createTextNode('🌿 ESRS E2 – Pollution'));

  const contextualIxbrlNodes = getContextualIxbrlNodes({
    ...sectionData,
    majorSection: MAJOR_SECTION.E2.code,
  });

  return [heading, ...contextualIxbrlNodes];
}
