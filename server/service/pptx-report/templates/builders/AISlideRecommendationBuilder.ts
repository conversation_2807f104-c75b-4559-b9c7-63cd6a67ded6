import { zodResponseFormat } from 'openai/helpers/zod';
import { UniversalTrackerPlain } from '../../../../models/universalTracker';
import { z } from 'zod';
import { AIModel, AIPrompt } from '../../../ai/models/AIModel';
import { ActionList } from '../../../utr/constants';
import { HelperUtrv } from '../PPTXTemplateSurveyCache';
import { PPTXTemplateSurveyCacheManager } from '../PPTXTemplateSurveyCacheManager';
import { UtrValueType } from '../../../../models/public/universalTrackerType';
import { SupportedMeasureUnits } from '../../../units/unitTypes';

const slideRecommendationDto = z.object({
  result: z.array(
    z.object({
      code: z.string(),
      status: z.string(),
      category: z.string(),
      score: z.number(),
    })
  ),
});

const DEFAULT_MAX_TOKEN = 10000;

type AnsweredUtr = Pick<
  UniversalTrackerPlain,
  'code' | 'valueLabel' | 'valueType' | 'instructions' | 'unitType' | 'unit' | 'numberScale'
> &
  Pick<HelperUtrv, 'status'>;

interface PPTXSlideRecommendationUtr extends Pick<AnsweredUtr, 'code' | 'status'> {
  environmental_category: string | null;
  environmental_relevance_score: number;
}

export class AISlideRecommendationBuilder {
  constructor(private aiModel: AIModel, private repositoryManager: PPTXTemplateSurveyCacheManager) {}

  public async getSlideRecommendations(): Promise<PPTXSlideRecommendationUtr[]> {
    const answeredUtrs = await this.getAnsweredUtrs();

    if (answeredUtrs.length === 0) {
      return [];
    }

    const prompt = this.generatePrompt(answeredUtrs);
    const responseFormat = zodResponseFormat(slideRecommendationDto, 'PPTXSlideRecommendation');
    const response = await this.aiModel.parseCompletion([prompt], DEFAULT_MAX_TOKEN, responseFormat);
    return response.content.result || [];
  }

  private async getAnsweredUtrs() {
    const utrvsMap = await this.repositoryManager.getCachedSurvey()?.getUtrvsMap();

    if (!utrvsMap) {
      return [];
    }

    return Array.from(utrvsMap.values()).reduce((acc, utrv) => {
      if (utrv.status === ActionList.Created || !utrv.universalTracker) {
        return acc;
      }

      acc.push({
        code: utrv.universalTracker.code,
        valueLabel: utrv.universalTracker.valueLabel,
        status: utrv.status,
        valueType: utrv.universalTracker.valueType,
        instructions: utrv.universalTracker.instructions,
        unitType: utrv.universalTracker.unitType,
        unit: utrv.universalTracker.unit,
        numberScale: utrv.universalTracker.numberScale,
      });

      return acc;
    }, [] as AnsweredUtr[]);
  }

  private generatePrompt(answeredUtrs: AnsweredUtr[]): AIPrompt {
    const content = `You are an AI assistant that recommends the most impactful environmental metrics for a company's sustainability report.

      You will receive a JSON array of metric objects. Each object will have the following structure:
      {
        "code": "The unique code of the metric",
        "valueType": "The type of value the metric represents, such as ${Object.values(UtrValueType).join(', ')}",
        "valueLabel": "The title of the metric",
        "instructions": "Any specific instructions or context provided for the metric",
        "unitType": "The type of unit used for the metric, such as ${Object.values(SupportedMeasureUnits).join(
          ', '
        )}, etc.",
        "unit": "The specific unit of measurement",
        "numberScale": "The scale of the number, such as thousands, millions, etc."
        "status": "The status of the metric, such as ${Object.values(ActionList).join(', ')}"
      }

      Here is the list of metrics: ${JSON.stringify(answeredUtrs)}

      Your primary task is to analyze a given list of company metrics and perform the following actions:

      Step 1: Classification
      - Iterate through the full list of input metrics. For each metric, determine if it is an environmental metric. Use the valueLabel, instructions, code, and unitType fields to make this determination.
      - Keywords to look for: "Emissions", "GHG", "Scope 1", "Scope 2", "Scope 3", "Carbon", "CO2", "Energy", "Water", "Waste", "Effluents", "Recycling", "Consumption", "Spills", "Environmental", "Biodiversity", "Land use", "Air quality", "NOx", "SOx".
      - Explicitly discard any metric where the unitType is non-environmental, such as 'currency', 'length', 'time' or other information indicates a social or economic topic (e.g., "Employee Turnover", "Revenue")
      - If the filtered list of environmental metrics is empty, the process stops here. Return a final JSON output of {"result": []} and do not proceed to the subsequent steps.

      Step 2: Scoring & Ranking
      - For each environmental metric identified in Step 1, evaluate its relevance. If the metric is not impactful enough for a high-level sustainability report, omit it from further processing.
      - Use the following scale as a guide:
        - **Score 0.9-1.0** Core, universally recognized environmental metrics (e.g., Total GHG, energy, and water consumption).
        - **Score between 0.7-0.9** Significant and widely reported environmental metrics that provide deeper insight into specific operational impacts (e.g., waste types, recycled water, renewable energy).
        -  **Omit any metric that does not meet the criteria for a score of 0.7 or higher.**

      Step 3: Categorize
      - Group each scored environmental metric into a logical environmental category and assign it to **category** (e.g., 'GHG Emissions', 'Energy Management', 'Waste', 'Significant Air Emissions', 'Biodiversity').
      - If a metric does not fit into an existing category, create a new category

      Step 4: Output Generation and Filtering
      - For each category, sort the metrics by score in descending order.
      - From each category, you must select a strict maximum of the top two metrics. If a category has only one metric, select that one. If it has two or more, select only the two with the highest scores.
      - Construct: Create the final output as a single JSON object with a key named "result". The value should be an array containing up to 2 metrics per category, combining to a maximum of 10 total metrics.
      - Each object in the final result array must have the following structure:
        {
          "code": "The 'code' from the original input metric",
          "status": "The 'status' from the original input metric",
          "score": "The score you assigned to the metric, from 0.7 to 1.0"
          "category": The category you assigned (e.g., "GHG Emissions"). Provide 'null' if the metric is not environmental.
        }

      Exclude any commentary or explanation — return only the structured JSON result as specified
    `;

    return {
      role: 'system',
      content,
    };
  }
}
