# Error Handling Patterns - G17Eco API

## Error Testing Patterns

### 1. User Error Testing
```typescript
import UserError from '../../../server/error/UserError';

it('should throw UserError for invalid input', async () => {
  await expect(service.method(invalidInput))
    .to.be.rejectedWith(UserError, 'Expected error message');
});
```

### 2. Error Message Wrapping
```typescript
it('should wrap external errors appropriately', async () => {
  sandbox.stub(externalService, 'method').rejects(externalError);
  
  await expect(service.method(input))
    .to.be.rejectedWith(InternalErrorType);
});
```

## Common Development Patterns

**Error Handling**: Custom error classes in `server/error/` with structured responses
**Background Jobs**: Queue-based processing with multiple backends (SQS, local)
**Audit Logging**: Comprehensive audit trails for data changes
**Permissions**: Role-based access control with organization/initiative scoping
**File Processing**: Multi-cloud file storage with automatic format conversion
**API Versioning**: Backward-compatible REST APIs with clear versioning strategy

## Anti-Patterns to Avoid

### 1. Testing Implementation Details
```typescript
// DON'T test internal private methods
expect(service._internalMethod).to.have.been.called;

// DO test public interface and outcomes
expect(service.publicMethod()).to.equal(expectedResult);
```

### 2. Overly Complex Test Setup
```typescript
// DON'T create massive setup in beforeEach
beforeEach(() => {
  // 50 lines of setup
});

// DO use focused, minimal setup per test
it('should test specific behavior', () => {
  const specificStub = sandbox.stub(service, 'method').returns(value);
  // Test logic
});
```

### 3. Testing Multiple Concerns
```typescript
// DON'T test multiple behaviors in one test
it('should validate input and save to database and send email', () => {
  // Too many responsibilities
});

// DO separate concerns
it('should validate input correctly', () => { /* ... */ });
it('should save to database', () => { /* ... */ });
it('should send email notification', () => { /* ... */ });
```

### 4. Brittle Assertions
```typescript
// DON'T rely on exact object structure
expect(result).to.equal(exactObjectWithAllProperties);

// DO focus on important properties
expect(result).to.include({
  status: 'success',
  data: expectedData
});
```