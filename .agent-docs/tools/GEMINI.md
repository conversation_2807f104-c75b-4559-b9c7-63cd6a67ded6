# Google Gemini Instructions - G17Eco API

This documentation provides context for Google Gemini when working with the G17Eco API codebase.

## Documentation Structure
The codebase documentation is organized into modular components:

### Core System Information
- [Architecture Overview](../core/architecture.md) - Layered architecture, database design, integrations
- [Core Commands](../core/commands.md) - Development workflow and commands

### Testing Guidelines
- [Testing Framework](../testing/framework.md) - Mocha/Chai/Sinon setup and file organization
- [Service Testing Patterns](../patterns/service-testing.md) - Testing templates and best practices
- [Error Handling Patterns](../patterns/error-handling.md) - Error testing strategies
- [Test Data Management](../patterns/test-data.md) - Fixtures, factories, and mock patterns

## Technology Stack
- **Runtime**: Node.js 22.14.0+
- **Language**: TypeScript (strict mode)
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Testing**: Mocha + Chai + Sinon
- **Coverage**: NYC (Istanbul)

## Code Organization Principles
1. **Domain-Driven Structure**: Services organized by business domain
2. **Layered Architecture**: Models → Repositories → Services → Routes
3. **Dependency Injection**: Services receive dependencies through constructors
4. **Error Handling**: Custom error classes with structured responses

## Development Workflow
1. Local development: `npm start`
2. Testing: `npm test` (required before commits)
3. Coverage: `npm run test-coverage`
4. Build: `npm run build-ts`

## Integration Points
- **Authentication**: Okta integration
- **Payments**: Stripe integration
- **Email**: AWS SES, Mailchimp
- **AI Services**: OpenAI GPT, Anthropic Claude
- **Storage**: Google Cloud, Azure Blob Storage