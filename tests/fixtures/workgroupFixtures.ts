import { ObjectId } from 'bson';
import { WorkgroupPlain } from '../../server/models/workgroup';
import { createSurveyPermission } from './survey';
import { SurveyWorkgroup } from '../../server/service/workgroup/SurveyWorkgroupService';

export const createWorkgroup = (overrides: Partial<WorkgroupPlain> = {}): WorkgroupPlain => ({
  _id: new ObjectId(),
  initiativeId: new ObjectId(),
  name: 'Workgroup name',
  description: 'Workgroup description',
  icon: 'fa-users',
  color: '#ffffff',
  users: [],
  creatorId: new ObjectId(),
  created: new Date(),
  updated: new Date(),
  ...overrides,
});

export const createSurveyWorkgroup = (overrides: Partial<SurveyWorkgroup> = {}): SurveyWorkgroup => {
  const workgroup = createWorkgroup();
  return {
    ...workgroup,
    permission: createSurveyPermission({ modelId: workgroup._id }),
    utrvIds: [],
    ...overrides,
  };
};
