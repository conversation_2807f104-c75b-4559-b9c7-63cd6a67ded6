/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { createSandbox } from 'sinon';
import { testLogger } from '../../../factories/logger';
import { surveyOne } from '../../../fixtures/survey';
import { userOne } from '../../../fixtures/userFixtures';
import { createInitiative } from '../../../fixtures/initiativeFixtures';
import fullStateSnapshot from './full-state.snapshot.json';
import UniversalTrackerValue from '../../../../server/models/universalTrackerValue';
import { createAggregate } from '../../../setup';
import { esrs2_E3_4 } from '../../../fixtures/utr/utrTableFixtures';
import {
  CSRDLexicalStateGenerator,
  getCSRDLexicalStateGenerator,
} from '../../../../server/service/reporting/csrd/CSRDLexicalStateGenerator';
import * as ESRSDefinitionsModule from '../../../../server/service/reporting/csrd/ESRSDefinitions';
import { getCSRDGenerator } from '../../../../server/service/reporting/csrd/CSRDGenerator';

describe('CSRDLexicalStateGenerator', () => {
  const sandbox = createSandbox();
  const csrdGenerator = new CSRDLexicalStateGenerator(testLogger, getCSRDGenerator());

  const initiative = createInitiative({
    name: 'Alpha Bravo Ltd.',
  });

  beforeEach(() => {
    // Mock ESRSDefinitions to a stable value to reduce comparison of too long object state
    // This is because of a mocha issue: maxDiffSize = 8192 characters
    sandbox.stub(ESRSDefinitionsModule, 'ESRSDefinitions').value({
      'esrs:WaterConsumption': { label: 'Water Consumption', technicalName: 'esrs:WaterConsumption' },
      'esrs:WaterConsumptionInAreasOfHighwaterStress': {
        label: 'Water Consumption In Areas Of HighwaterStress',
        technicalName: 'esrs:WaterConsumptionInAreasOfHighwaterStress',
      },
      'esrs:DataSourcesWaterRecycledAndReused': {
        label: 'Data Sources Water Recycled And Reused',
        technicalName: 'esrs:DataSourcesWaterRecycledAndReused',
      },
      'esrs:WaterStored': { label: 'Water Stored', technicalName: 'esrs:WaterStored' },
      'esrs:IncreaseDecreaseInWaterStorage': {
        label: 'Increase Decrease In Water Storage',
        technicalName: 'esrs:IncreaseDecreaseInWaterStorage',
      },
      'esrs:WaterIntensityTotalWaterConsumptionPerNetRevenue': {
        label: 'Water Intensity Total Water Consumption Per Net Revenue',
        technicalName: 'esrs:WaterIntensityTotalWaterConsumptionPerNetRevenue',
      },
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    const instance = getCSRDLexicalStateGenerator();
    expect(instance).to.be.instanceOf(CSRDLexicalStateGenerator);
  });

  // Temporary skip this test until the feature is fully developed
  it.skip('process report and return status', async () => {
    const mockUtrv = esrs2_E3_4;
    sandbox.stub(UniversalTrackerValue, 'aggregate').returns(createAggregate([mockUtrv]));

    const state = await csrdGenerator.generateCSRDLexicalState({
      survey: surveyOne,
      initiative: initiative,
      mapping: {},
      utrCodeToUtrvMap: new Map([[mockUtrv.universalTracker.code, mockUtrv]]),
      user: userOne,
      preview: true,
    });
    
    expect(state).to.deep.equal(fullStateSnapshot);
  });
});
