import { expect } from 'chai';
import {
  getData,
  getStringData,
  getFactLabel,
  getFactSectionAndDataPoint,
  getMajorSection,
  getOrderedMappingItems,
} from '../../../../server/service/reporting/csrd/utils';
import { XBRLMapping, UtrvData } from '../../../../server/service/reporting/csrd/types';
import { createExtendedUtrv } from '../../../../tests/fixtures/compositeUTRVFixtures';
import { UtrValueType } from '../../../../server/models/public/universalTrackerType';
import { NumberScale } from '../../../../server/service/units/unitTypes';
import { ObjectId } from 'bson';
import { utrOneId, utrTwoId } from '../../../../tests/fixtures/universalTrackerFixtures';

describe('utils', () => {
  const factOne = 'esrs:BasisForPreparationOfSustainabilityStatement'; // Section: "BP-1", dataPointId: "BP-1_01"
  const factTwo = 'esrs:PercentageOfTotalEmissionsOfPollutantsToSoilOccurringInAreasAtWaterRisk'; // Section: "E2-4", dataPointId: "E2-4_13"
  const factThree = 'esrs:GeneralBasisForPreparationOfSustainabilityStatementAbstract'; // No section, dataPointId
  const noRefFact = 'esrs:NoRefFact';

  // Types from types.ts
  const mapping: XBRLMapping = {
    [factOne]: { factName: factOne, utrCode: 'utr1' },
    [factTwo]: { factName: factTwo, utrCode: 'utr2' },
    [factThree]: { factName: factThree, utrCode: 'utr3' },
  };

  const utrvId1 = new ObjectId();
  const utrvId2 = new ObjectId();

  const utrCodeToUtrvMap: Map<string, UtrvData> = new Map([
    [
      'utr1',
      createExtendedUtrv({
        id: utrvId1,
        utrId: utrOneId,
        value: 123,
        overridesUtr: { code: 'utr1', valueType: UtrValueType.Number, numberScale: NumberScale.Hundreds, unit: 'm3' },
      }),
    ],
    [
      'utr2',
      createExtendedUtrv({
        id: utrvId2,
        utrId: utrTwoId,
        overrides: { valueData: { data: 'abc' } },
        overridesUtr: { code: 'utr2', valueType: UtrValueType.Text, numberScale: undefined, unit: undefined },
      }),
    ],
  ]);

  describe('getData', () => {
    it('returns value for number type', () => {
      expect(getData({ factName: factOne, mapping, utrCodeToUtrvMap })).to.equal(123);
    });
    it('returns value for text type', () => {
      expect(getData({ factName: factTwo, mapping, utrCodeToUtrvMap })).to.equal('abc');
    });
    it('returns fallback for missing mapping', () => {
      expect(getData({ factName: factThree, mapping, utrCodeToUtrvMap, fallback: 'fallback' })).to.equal('fallback');
    });
    it('returns fallback for missing utrv', () => {
      expect(getData({ factName: noRefFact, mapping, utrCodeToUtrvMap, fallback: 'fallback' })).to.equal('fallback');
    });
  });

  describe('getStringData', () => {
    it('returns string value', () => {
      expect(getStringData({ factName: factOne, mapping, utrCodeToUtrvMap })).to.equal('123');
    });
    it('returns fallback as string', () => {
      expect(getStringData({ factName: noRefFact, mapping, utrCodeToUtrvMap, fallback: 'fallback' })).to.equal(
        'fallback'
      );
    });
  });

  describe('getFactLabel', () => {
    it('returns label if present', () => {
      expect(getFactLabel(factOne)).to.equal('Basis for preparation of sustainability statement');
    });
    it('returns empty string if not present', () => {
      expect(getFactLabel(noRefFact)).to.equal('');
    });
  });

  describe('getFactSectionAndDataPoint', () => {
    it('returns section and dataPointId', () => {
      expect(getFactSectionAndDataPoint(factOne)).to.deep.equal({ section: 'BP-1', dataPointId: 'BP-1_01' });
    });
    it('returns undefined for missing references', () => {
      expect(getFactSectionAndDataPoint(noRefFact)).to.be.undefined;
    });
  });

  describe('getMajorSection', () => {
    it('extracts E1 from E1-1', () => {
      expect(getMajorSection('E1-1')).to.equal('E1');
    });
    it('extracts SBM from SBM-2', () => {
      expect(getMajorSection('SBM-2')).to.equal('SBM');
    });
    it('returns undefined for unknown section', () => {
      expect(getMajorSection('XYZ-1')).to.be.undefined;
    });
    it('returns undefined for undefined', () => {
      expect(getMajorSection(undefined)).to.be.undefined;
    });
  });

  describe('getOrderedMappingItems', () => {
    it('returns only items matching majorSection', () => {
      const result = getOrderedMappingItems({ mapping, majorSection: 'BP' });
      expect(result).to.have.lengthOf(1);
      expect(result[0].factName).to.equal(factOne);
    });
    it('returns empty for non-matching majorSection', () => {
      expect(getOrderedMappingItems({ mapping, majorSection: 'E4' })).to.have.lengthOf(0);
    });
  });
});
