/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { expect } from 'chai';
import { createSandbox } from 'sinon';
import { testLogger } from "../../../factories/logger";
import { CSRDGenerator, getCSRDGenerator } from "../../../../server/service/reporting/csrd/CSRDGenerator";
import { surveyOne } from "../../../fixtures/survey";
import { userOne } from "../../../fixtures/userFixtures";
import { createInitiative } from "../../../fixtures/initiativeFixtures";
import { getXhtmlGenerator } from "../../../../server/service/reporting/xhtml/XHTMLGenerator";
import { writeFile } from "fs/promises";
import UniversalTrackerValue from "../../../../server/models/universalTrackerValue";
import { createAggregate } from "../../../setup";
import { esrs2_E3_4 } from "../../../fixtures/utr/utrTableFixtures";

describe('CSRDGenerator', () => {
  const sandbox = createSandbox();
  const xmlGenerator = getXhtmlGenerator();
  const csrdGenerator = new CSRDGenerator(testLogger, xmlGenerator);


  const initiative = createInitiative({
    name: 'Alpha Bravo Ltd.',
  })

  beforeEach(() => {
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should create instance', () => {
    const instance = getCSRDGenerator();
    expect(instance).to.be.instanceOf(CSRDGenerator);
  });
  
  // Temporary skip this test until the feature is fully developed
  it.skip('process report and return status', async () => {
    sandbox.stub(UniversalTrackerValue, 'aggregate').returns(createAggregate([
      esrs2_E3_4
    ]));

    const result = await csrdGenerator.generateCSRD({
      survey: surveyOne,
      initiative: initiative,
      mapping: {},
      preview: true,
      sections: [],
    });

    const { tracker, xmlString } = result;
    // Write for testing, temp
    await writeFile(__dirname + `/__temp_G17Eco-ESRS-XBRL-sample1.xhtml`, xmlString);

    expect(xmlString).to.contain(initiative.name);
    
    const facts = tracker.getDebugFacts();
    expect(facts).to.have.property('fact-1');

    const factOne = facts['fact-1'];

    expect(factOne?.a?.c).to.eq('esrs:WaterConsumption');
    expect(factOne?.a?.u).to.eq('m3');
    expect(factOne?.v).to.eq('100');
  });
});
