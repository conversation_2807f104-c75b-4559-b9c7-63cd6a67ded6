/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { expect } from "chai";
import sinon, { createSandbox } from 'sinon';
import {
  AssessmentData,
  AssessmentInputs,
  AssessmentResult,
} from '../../../server/service/materiality-assessment/types';
import { MaterialityAssessmentService } from '../../../server/service/materiality-assessment/MaterialityAssessmentService';
import { MaterialTopicPlain } from '../../../server/models/materialTopics';
import { MaterialityMetricPlain, MaterialityMetricWithUtrAndValueListPlain } from '../../../server/models/materialityMetric';
import {
  MaterialityMetricRepository
} from '../../../server/service/materiality-assessment/MaterialityMetricRepository';
import { MaterialTopicRepository } from '../../../server/service/materiality-assessment/MaterialTopicRepository';
import {
  CoreQuestion
} from '../../../server/service/materiality-assessment/assessments/MaterialityLookupCalculator';
import { ObjectId } from 'bson';
import { SurveyRepository } from "../../../server/repository/SurveyRepository";
import { createCombinedFromCode, } from "../../factories/universalTrackerValue";
import UniversalTracker, { UniversalTrackerPlain } from "../../../server/models/universalTracker";
import { createSurveyAction } from "../../fixtures/survey";
import { SurveyActionMinimalUtrv } from "../../../server/models/survey";
import { ActionList } from "../../../server/service/utr/constants";
import { UtrValueType } from "../../../server/models/public/universalTrackerType";
import { InitiativeRepository } from "../../../server/repository/InitiativeRepository";
import { initiativeOneSimple } from "../../fixtures/initiativeFixtures";
import { utrForBlueprintProject } from '../../../server/repository/projections';
import { createMongooseModel } from '../../setup';
import config from "../../../server/config";

interface MaterialityAssessmentTest {
  name: string;
  maxScore: number;
  inputs: AssessmentInputs;
  expects: AssessmentResult['financial'];
}

describe('MaterialityAssessmentService', async () => {
  const sandbox = createSandbox();
  let findStub: sinon.SinonStub;

  const materialityMetrics: Pick<MaterialityMetricPlain, 'utrCode' | 'options'>[] = [
    {
      utrCode: CoreQuestion.NumStaff,
      options: [
        {
          optionCode: 'blue',
          scores: [
            {
              materialityCode: 'Mat1',
              score: 1,
            },
            {
              materialityCode: 'Mat2',
              score: 10,
            },
            {
              materialityCode: 'Mat4',
              score: 100,
            },
          ],
        },
      ],
    },
    {
      utrCode: CoreQuestion.StakeholderEngagement,
      options: [
        {
          optionCode: 'blue',
          scores: [
            {
              materialityCode: 'Mat1',
              score: 5,
            },
            {
              materialityCode: 'Mat2',
              score: 50,
            },
            {
              materialityCode: 'Mat4',
              score: 500,
            },
          ],
        },
      ],
    },
    {
      utrCode: CoreQuestion.OperationsSituated,
      options: [
        {
          optionCode: 'locA',
          scores: [
            {
              materialityCode: 'Mat1',
              score: 1,
            },
            {
              materialityCode: 'Mat2',
              score: 10,
            },
            {
              materialityCode: 'Mat3',
              score: 100,
            },
          ],
        },
        {
          optionCode: 'locB',
          scores: [
            {
              materialityCode: 'Mat2',
              score: 99,
            },
            {
              materialityCode: 'Mat4',
              score: 999,
            },
          ],
        },
      ],
    },
  ];

  beforeEach(() => {
    sandbox.stub(MaterialityMetricRepository, 'findFinancialByUtrCodes').resolves(materialityMetrics as MaterialityMetricWithUtrAndValueListPlain<ObjectId>[]);
    sandbox.stub(MaterialityMetricRepository, 'findImpactByUtrCodes').resolves(materialityMetrics as MaterialityMetricWithUtrAndValueListPlain<ObjectId>[]);
    sandbox.stub(MaterialTopicRepository, 'findByCodes').resolves([] as MaterialTopicPlain[]);
    sandbox.stub(InitiativeRepository, 'mustFindById').resolves(initiativeOneSimple as any);
    findStub = sandbox.stub(UniversalTracker as any, 'find').returns(createMongooseModel([]));
  });
  afterEach(() => {
    sandbox.restore();
  })

  const newCalculation = config.features.materialityAssessment.useNewScoreCalculation;
  const tests: MaterialityAssessmentTest[] = [
    {
      name: 'Test 1 - no valid inputs',
      maxScore: newCalculation ? 10000 : 0,
      inputs: new Map<CoreQuestion, string[]>([
        [CoreQuestion.NumStaff, ['red']],
        [CoreQuestion.StakeholderEngagement, ['red']],
      ]),
      expects: [],
    },
    {
      name: 'StakeholderEngagement scores',
      inputs: new Map<CoreQuestion, string[]>([
        [CoreQuestion.StakeholderEngagement, ['blue']],
      ]),
      maxScore: 10000,
      expects: [
        {
          code: 'Mat1',
          score: 100,
          relativeScore: newCalculation ? 25.11 : 1,
        },
        {
          code: 'Mat2',
          score: 1000,
          relativeScore: newCalculation ? 56.27 : 10,
        },
        {
          code: 'Mat4',
          score: 10000,
          relativeScore: 100,
        },
      ],
    },
    {
      name: 'NumStaff Scores',
      inputs: new Map([
        [CoreQuestion.NumStaff, ['blue']],
      ]),
      maxScore: newCalculation ? 10000 : 0,
      expects: [
        {
          code: 'Mat1',
          score: 30,
          relativeScore: newCalculation ? 13.9 : undefined,
        },
        {
          code: 'Mat2',
          score: 300,
          relativeScore: newCalculation ? 38.39 : undefined,
        },
        {
          code: 'Mat4',
          score: 3000,
          relativeScore: newCalculation ? 75.57 : undefined,
        },
      ],
    },
    {
      name: 'NumStaff and StakeholderEngagement should return scores',
      inputs: new Map<CoreQuestion, string[]>([
        [CoreQuestion.NumStaff, ['blue']],
        [CoreQuestion.StakeholderEngagement, ['blue']],
      ]),
      maxScore: newCalculation ? 10000 : 1,
      // @TODO is the issue here that codes overlap? we only get NumStaff scores?
      expects: [
        {
          code: 'Mat1',
          score: 130,
          relativeScore: newCalculation ? 28.02 : 13000,
        },
        {
          code: 'Mat2',
          score: 1300,
          relativeScore: newCalculation ? 60.62 : 130000,
        },
        {
          code: 'Mat4',
          score: 13000,
          relativeScore: newCalculation ? 105.78 : 1300000,
        },
      ],
    },
    {
      name: 'Locations can have multiple',
      inputs: new Map<CoreQuestion, string[]>([
        [CoreQuestion.OperationsSituated, ['locA', 'locB']],
      ]),
      maxScore: newCalculation ? 10000 : 1,
      expects: [
        {
          code: 'Mat1',
          score: 3.2, // (1 + 0) * 100 * 0.032
          relativeScore: newCalculation ? 2.43 : 320,
        },
        {
          code: 'Mat2',
          score: 348.8, // (10 + 99) * 100 * 0.032
          relativeScore: newCalculation ? 40.44 : 34880,
        },
        {
          code: 'Mat3',
          score: 320, // (100 + 0) * 100 * 0.032
          relativeScore: newCalculation ? 39.27 : 32000,
        },
        {
          code: 'Mat4',
          score: 3196.8, // (0 + 999) * 100 * 0.032
          relativeScore: newCalculation ? 76.77 : 319680,
        },
      ],
    },
  ];

  for (const test of tests) {
    it(`Test: ${test.name}`, async () => {

      sandbox.stub(MaterialTopicRepository, 'findMaxScore').resolves(test.maxScore);
      const utrvs: SurveyActionMinimalUtrv[] = [];
      const utrs: UniversalTrackerPlain[] = [];
      test.inputs.forEach((p, key) => {
        const utrv = createCombinedFromCode(key, {
          status: ActionList.Verified,
          valueData: { data: p }
        }, {
          valueType: UtrValueType.ValueList,
        });
        utrvs.push(utrv);
        utrs.push(utrv.universalTracker)
      });

      const survey = createSurveyAction({
        fragmentUniversalTrackerValues: utrvs,
        fragmentUniversalTracker: utrs,
      });
      sandbox.stub(SurveyRepository, 'getInitiativeSurveyUtrvActions').resolves([survey]);

      const service = new MaterialityAssessmentService(new ObjectId());

      // Only testing financial?
      const { financial, nonFinancial } = await service.getResult();
      expect(financial).to.eql(test.expects);
    });
  }

  describe('getAssessmentMappedUtrs', () => {
    it('should return empty array with empty AssessmentResult', async () => {
      const service = new MaterialityAssessmentService(new ObjectId());
      const result: AssessmentResult = { financial: [], nonFinancial: [] };
      sandbox.stub(service, 'hydrateTopics').resolves(result);
      await service.getAssessmentMappedUtrs(result);
      expect(findStub.calledWith({ code: { $in: [] } }, utrForBlueprintProject)).to.be.true;
    });

    it('should return utrs with AssessmentResult containing both financial and non-financial data', async () => {
      const service = new MaterialityAssessmentService(new ObjectId());
      const result: AssessmentResult<AssessmentData> = {
        financial: [{ code: 'topic-code-1', score: 1, utrMapping: [{ code: 'utr-1', score: 1 }] }],
        nonFinancial: [{ code: 'topic-code-2', score: 1, utrMapping: [{ code: 'utr-2', score: 1 }] }],
      };
      sandbox.stub(service, 'hydrateTopics').resolves(result);
      await service.getAssessmentMappedUtrs(result);
      expect(findStub.calledWith({ code: { $in: ['utr-1', 'utr-2'] } }, utrForBlueprintProject)).to.be.true;
    });
  });
});
